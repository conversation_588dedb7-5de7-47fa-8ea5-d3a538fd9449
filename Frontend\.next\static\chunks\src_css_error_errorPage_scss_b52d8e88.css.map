{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/css/error/errorPage.scss.css"], "sourcesContent": ["@media(max-width: 991px){.w-md-100{width:100%}.p-md-200{padding:0px 200px}}@media(max-width: 700px){.p-md-200{padding:0px 0px}}.errorPage{height:calc(70vh - 90px);padding:50px 0;display:flex;align-items:center;justify-content:center;padding-inline:1rem}.errorPage_inner{display:flex;justify-content:center;align-items:center}@media(max-width: 991px){.errorPage_inner{flex-direction:column}}.errorPage_inner figure{width:300px}@media(max-width: 991px){.errorPage_inner figure{width:200px}}.errorPage_content{padding-left:2rem}@media(max-width: 991px){.errorPage_content{padding-left:0;text-align:center;padding-top:2rem}}.errorPage_content p{margin-bottom:1rem;font-size:1.25rem}@media(max-width: 991px){.errorPage_content p{font-size:1rem}}.errorPage_content .commonSearch{position:relative}@media(max-width: 991px){.errorPage_content .commonSearch{width:100%;max-width:400px;justify-content:center;margin:0 auto}.errorPage_content .commonSearch .form-control{width:100%}}.w-400px{width:400px !important}"], "names": [], "mappings": "AAAA;EAAyB;;;;EAAqB;;;;;AAA6B;EAAyB;;;;;AAA2B;;;;;;;;;AAA8H;;;;;;AAAwE;EAAyB;;;;;AAAwC;;;;AAAoC;EAAyB;;;;;AAAqC;;;;AAAqC;EAAyB;;;;;;;AAAsE;;;;;AAA0D;EAAyB;;;;;AAAqC;;;;AAAmD;EAAyB;;;;;;;EAAiG;;;;;AAA2D"}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}