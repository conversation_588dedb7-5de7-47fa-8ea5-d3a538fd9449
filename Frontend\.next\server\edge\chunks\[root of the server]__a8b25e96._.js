(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root of the server]__a8b25e96._.js", {

"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[project]/src/utils/environment.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
/**
 * Environment Detection Utility
 * 
 * Provides consistent environment detection and URL handling
 * across the frontend application for both client and server-side code.
 */ /**
 * Detect the current environment
 * @returns {string} 'development' | 'production'
 */ __turbopack_context__.s({
    "ENV_CONFIG": (()=>ENV_CONFIG),
    "getApiBaseUrl": (()=>getApiBaseUrl),
    "getBaseUrlForParsing": (()=>getBaseUrlForParsing),
    "getCurrentOrigin": (()=>getCurrentOrigin),
    "getEnvConfig": (()=>getEnvConfig),
    "getEnvironment": (()=>getEnvironment),
    "isDevelopment": (()=>isDevelopment),
    "isProduction": (()=>isProduction)
});
function getEnvironment() {
    // Check NODE_ENV first
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // In browser, check the hostname
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // Default to development for safety
    return 'development';
}
function getCurrentOrigin() {
    // In browser environment
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // In server-side environment, determine based on environment
    const env = getEnvironment();
    if (env === 'production') {
        // Check if we're on dev subdomain or main domain
        if (("TURBOPACK compile-time value", "http://127.0.0.1:8000")?.includes('dev.tradereply.com')) {
            return 'https://dev.tradereply.com';
        }
        return 'https://tradereply.com';
    }
    // Development fallback
    return 'http://localhost:3000';
}
function getApiBaseUrl() {
    // Use environment variable if available
    if ("TURBOPACK compile-time truthy", 1) {
        return "TURBOPACK compile-time value", "http://127.0.0.1:8000";
    }
    "TURBOPACK unreachable";
    // Fallback based on environment
    const env = undefined;
}
function isDevelopment() {
    return getEnvironment() === 'development';
}
function isProduction() {
    return getEnvironment() === 'production';
}
function getBaseUrlForParsing() {
    return getCurrentOrigin();
}
const ENV_CONFIG = {
    development: {
        domains: [
            'localhost',
            '127.0.0.1',
            '::1',
            '.local',
            '.dev',
            '.test'
        ],
        defaultOrigin: 'http://localhost:3000',
        defaultApiBase: 'http://127.0.0.1:8000',
        secure: false
    },
    production: {
        domains: [
            'tradereply.com',
            'dev.tradereply.com',
            'www.tradereply.com'
        ],
        defaultOrigin: 'https://dev.tradereply.com',
        defaultApiBase: 'https://dev.tradereply.com',
        secure: true
    }
};
function getEnvConfig() {
    const env = getEnvironment();
    return ENV_CONFIG[env];
}
}}),
"[project]/src/config/securityRoutes.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
/**
 * Security Routes Configuration
 *
 * This file contains the security-protected routes configuration that is used
 * by the Next.js middleware and other frontend components. This configuration
 * is self-contained within the frontend to avoid dependencies on backend APIs.
 *
 * To update this configuration:
 * 1. Modify the constants below directly in this file
 * 2. Update the corresponding backend config/security.php if needed for backend middleware
 * 3. Redeploy the frontend
 *
 * Note: This is the single source of truth for frontend security route configuration.
 */ /**
 * Security-protected routes that require verification
 * Add new routes here when implementing new secure pages
 */ __turbopack_context__.s({
    "AUTH_FLOW_PROTECTED_ROUTES": (()=>AUTH_FLOW_PROTECTED_ROUTES),
    "CONFIG_METADATA": (()=>CONFIG_METADATA),
    "DEFAULT_FALLBACK_URL": (()=>DEFAULT_FALLBACK_URL),
    "FALLBACK_URL_MAPPINGS": (()=>FALLBACK_URL_MAPPINGS),
    "INVALID_REFERRER_PATHS": (()=>INVALID_REFERRER_PATHS),
    "SECURITY_CONFIG": (()=>SECURITY_CONFIG),
    "SECURITY_PROTECTED_ROUTES": (()=>SECURITY_PROTECTED_ROUTES),
    "VALID_AUTH_FLOW_REFERRERS": (()=>VALID_AUTH_FLOW_REFERRERS),
    "VALID_REFERRER_PREFIXES": (()=>VALID_REFERRER_PREFIXES),
    "VALID_SECURITY_CHECK_REFERRERS": (()=>VALID_SECURITY_CHECK_REFERRERS),
    "getAuthFlowFallbackUrl": (()=>getAuthFlowFallbackUrl),
    "getDirectAccessFallbackUrl": (()=>getDirectAccessFallbackUrl),
    "isValidAuthFlowRoute": (()=>isValidAuthFlowRoute),
    "isValidSecureRoute": (()=>isValidSecureRoute)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$environment$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/environment.js [middleware-edge] (ecmascript)");
const SECURITY_PROTECTED_ROUTES = [
    '/account/phone/setup',
    '/account/email/setup',
    // '/account/password/change',
    // '/account/2fa/setup',
    '/account/security/two-factor',
    '/account/username/setup',
    '/account/address/manage',
    '/account/address/setup'
];
const AUTH_FLOW_PROTECTED_ROUTES = [
    '/create-username',
    '/change-password'
];
const VALID_REFERRER_PREFIXES = [
    '/account',
    '/dashboard',
    '/user',
    '/marketplace',
    '/pricing',
    '/help',
    '/settings',
    '/'
];
const INVALID_REFERRER_PATHS = [
    '/login',
    '/signup',
    '/forget-password',
    '/verify-email',
    '/security-check',
    '/logout'
];
const FALLBACK_URL_MAPPINGS = {
    '/account/phone/setup': '/account/details',
    '/account/email/setup': '/account/details',
    // '/account/password/change': '/account/overview',
    // '/account/2fa/setup': '/account/overview',
    '/account/security/two-factor': '/account/security',
    '/account/username/setup': '/account/details',
    '/account/address/manage': '/account/details',
    '/account/address/setup': '/account/address/manage'
};
const DEFAULT_FALLBACK_URL = '/account/overview';
const VALID_SECURITY_CHECK_REFERRERS = [
    '/account',
    '/dashboard',
    '/user'
];
const VALID_AUTH_FLOW_REFERRERS = [
    '/security-check'
];
const SECURITY_CONFIG = {
    // Enable referrer-based access control
    referrerControlEnabled: true,
    // Cookie configuration for client-side validation
    cookie: {
        name: 'security_verified',
        // Note: Actual expiration is controlled by backend, this is for client-side validation only
        defaultExpirationMinutes: 5,
        checkIntervalSeconds: 30
    }
};
;
function isValidSecureRoute(path) {
    try {
        // Decode URL if it's encoded
        const decodedPath = decodeURIComponent(path);
        // For relative paths, extract path directly without URL constructor
        let pathOnly;
        if (decodedPath.startsWith('/')) {
            // It's already a relative path, use it directly
            pathOnly = decodedPath.split('?')[0]; // Remove query parameters
        } else {
            // It might be an absolute URL, use URL constructor with dynamic base
            const baseUrl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$environment$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["getBaseUrlForParsing"])();
            const url = new URL(decodedPath, baseUrl);
            pathOnly = url.pathname;
        }
        // Check if the path exactly matches any of the protected routes
        // or if it starts with any of the protected routes (for sub-paths)
        return SECURITY_PROTECTED_ROUTES.some((route)=>{
            // Exact match
            if (pathOnly === route) {
                return true;
            }
            // Check if path starts with the route (for sub-paths like /account/phone/setup/step2)
            if (pathOnly.startsWith(route + '/')) {
                return true;
            }
            return false;
        });
    } catch (error) {
        console.warn('Error validating secure route:', error);
        return false;
    }
}
function getDirectAccessFallbackUrl(securePagePath) {
    return FALLBACK_URL_MAPPINGS[securePagePath] || DEFAULT_FALLBACK_URL;
}
function isValidAuthFlowRoute(path) {
    try {
        // Decode URL if it's encoded
        const decodedPath = decodeURIComponent(path);
        // For relative paths, extract path directly without URL constructor
        let pathOnly;
        if (decodedPath.startsWith('/')) {
            // It's already a relative path, use it directly
            pathOnly = decodedPath.split('?')[0]; // Remove query parameters
        } else {
            // It might be an absolute URL, use URL constructor with dynamic base
            const baseUrl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$environment$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["getBaseUrlForParsing"])();
            const url = new URL(decodedPath, baseUrl);
            pathOnly = url.pathname;
        }
        // Check if the path exactly matches any of the auth flow protected routes
        return AUTH_FLOW_PROTECTED_ROUTES.some((route)=>{
            // Exact match
            if (pathOnly === route) {
                return true;
            }
            // Check if path starts with the route (for sub-paths)
            if (pathOnly.startsWith(route + '/')) {
                return true;
            }
            return false;
        });
    } catch (error) {
        console.warn('Error validating auth flow route:', error);
        return false;
    }
}
function getAuthFlowFallbackUrl(attemptedPath) {
    // Map specific auth flow routes to their appropriate fallback pages
    const authFlowFallbacks = {
        '/create-username': '/login'
    };
    // Check for specific route fallback first
    if (authFlowFallbacks[attemptedPath]) {
        return authFlowFallbacks[attemptedPath];
    }
    // Default fallback for auth flow routes
    return '/login';
}
const CONFIG_METADATA = {
    lastSynced: new Date().toISOString(),
    source: 'backend:config/security.php',
    version: '1.0.0'
};
}}),
"[project]/src/middleware.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "middleware": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$securityRoutes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/config/securityRoutes.js [middleware-edge] (ecmascript)");
;
;
/**
 * Validate security verification cookie
 */ function isValidSecurityCookie(cookieValue) {
    if (!cookieValue) {
        return false;
    }
    // Simple validation for 'true' value (legacy)
    if (cookieValue === 'true') {
        return true;
    }
    // Simple validation for 'verified' value (fallback)
    if (cookieValue === 'verified') {
        return true;
    }
    // Check if it's a session token (64 character hex string)
    if (typeof cookieValue === 'string' && cookieValue.length === 64 && /^[a-f0-9]+$/i.test(cookieValue)) {
        // For session tokens, we assume they're valid on client side
        // Backend will do the authoritative validation
        return true;
    }
    // Check if it's a UUID format (with hyphens)
    if (typeof cookieValue === 'string' && /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i.test(cookieValue)) {
        // For UUID session tokens, we assume they're valid on client side
        // Backend will do the authoritative validation
        return true;
    }
    // Validate old encrypted payload format for backward compatibility
    try {
        const payload = JSON.parse(atob(cookieValue));
        if (payload.verified_at) {
            const verifiedAt = new Date(payload.verified_at);
            const now = new Date();
            const diffInSeconds = (now.getTime() - verifiedAt.getTime()) / 1000;
            // Check if within reasonable window (backend will do authoritative validation)
            // Use 15 minutes as a safe buffer since backend config may vary
            return diffInSeconds <= 900;
        }
    } catch (e) {
        // Invalid cookie format
        return false;
    }
    return false;
}
function middleware(req) {
    const token = req.cookies.get('authToken');
    const authFlow = req.cookies.get('auth_flow_verified');
    const pathname = req.nextUrl.pathname;
    const ref = req.nextUrl.searchParams.get('ref');
    const sessionId = req.nextUrl.searchParams.get('session_id');
    const activeSubscription = req.cookies.get('active_subscription');
    const isFreeUser = !activeSubscription || activeSubscription?.plan?.billing_type === 'free';
    const isLoggedIn = !!token;
    const firstLogin = req.cookies.get('first_login')?.value || req.cookies.get('first_login');
    const returningLogin = req.cookies.get('returning_login')?.value || req.cookies.get('returning_login');
    const clickPaidButton = req.cookies.get('click_paid_button')?.value || req.cookies.get('click_paid_button');
    const authRoutes = [
        '/login',
        '/signup',
        '/forget-password',
        '/verify-email',
        '/home',
        '/create-username'
    ];
    const protectedPrefixes = [
        '/user',
        '/account',
        '/dashboard',
        '/super-admin',
        '/not-found'
    ];
    const flowProtectedRoutes = [
        '/change-password'
    ];
    // ✅ Security verification protected pages (from centralized configuration)
    const securityProtectedRoutes = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$securityRoutes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SECURITY_PROTECTED_ROUTES"];
    // Helper function to validate if a path is a valid secure route (from centralized configuration)
    // Note: Using imported function from securityRoutes config
    // Helper function to check if user arrived via legitimate navigation (not direct URL access)
    const hasValidNavigationReferrer = (request)=>{
        const referrer = request.headers.get('referer');
        const currentPath = request.nextUrl.pathname;
        // No referrer indicates direct URL access (typed in browser, bookmark, etc.)
        if (!referrer) {
            return false;
        }
        // Special handling for security-check page access
        if (currentPath.startsWith('/security-check')) {
            // For security-check page, only allow access from secure pages or middleware redirects
            try {
                const referrerUrl = new URL(referrer);
                const referrerPath = referrerUrl.pathname;
                // ✅ Special case: Allow access from signup page when signup parameter is present
                const isSignupFlow = req.nextUrl.searchParams.has('signup') && referrerPath === '/signup';
                // Allow access from secure pages (when middleware redirects users to security-check)
                const isFromSecurePage = securityProtectedRoutes.some((route)=>referrerPath.startsWith(route));
                // Allow access from valid application pages that might trigger security verification
                const validSecurityCheckReferrers = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$securityRoutes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["VALID_SECURITY_CHECK_REFERRERS"];
                const isFromValidPage = validSecurityCheckReferrers.some((prefix)=>referrerPath.startsWith(prefix));
                console.log('Security-check referrer validation:', {
                    referrerPath: referrerPath,
                    isFromSecurePage: isFromSecurePage,
                    isFromValidPage: isFromValidPage,
                    isSignupFlow: isSignupFlow
                });
                return isFromSecurePage || isFromValidPage || isSignupFlow;
            } catch (error) {
                console.warn('Error parsing referrer URL for security-check:', error);
                return false;
            }
        }
        // Special case: Allow referrers from security-check page (after successful verification)
        try {
            const referrerUrl = new URL(referrer);
            if (referrerUrl.pathname.startsWith('/security-check')) {
                console.log('MIDDLEWARE: Allowing navigation from security-check page:', {
                    referrer: referrer,
                    referrerPath: referrerUrl.pathname,
                    currentPath: currentPath
                });
                return true; // Allow navigation from security-check page
            }
        } catch (error) {
            console.warn('MIDDLEWARE: Error parsing referrer URL:', error);
        // Continue with normal validation if URL parsing fails
        }
        // Validate that referrer is from a valid application page
        return isValidApplicationReferrer(referrer, request);
    };
    // Helper function to check if the referrer is from a valid application page
    const isValidApplicationReferrer = (referrer, request)=>{
        try {
            const parsedUrl = new URL(referrer);
            // Domain comparison logic removed to ensure consistent behavior across environments
            // Get the referrer path
            const referrerPath = parsedUrl.pathname;
            // Don't allow referrers from invalid pages (from centralized configuration)
            // Note: /security-check is handled specially in hasValidNavigationReferrer
            if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$securityRoutes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["INVALID_REFERRER_PATHS"].some((invalidPage)=>referrerPath.startsWith(invalidPage))) {
                return false;
            }
            // Allow referrers from valid application pages (from centralized configuration)
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$securityRoutes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["VALID_REFERRER_PREFIXES"].some((validPrefix)=>referrerPath.startsWith(validPrefix));
        } catch (error) {
            console.warn('Error validating referrer:', error);
            return false;
        }
    };
    // Helper function to validate auth flow referrers
    const hasValidAuthFlowReferrer = (req)=>{
        const referrer = req.headers.get('referer');
        const currentPath = req.nextUrl.pathname;
        if (!referrer) {
            console.log('Auth flow referrer validation: No referrer found');
            return false;
        }
        try {
            const referrerUrl = new URL(referrer);
            const referrerPath = referrerUrl.pathname;
            console.log('Auth flow referrer validation:', {
                currentPath: currentPath,
                referrer: referrer,
                referrerPath: referrerPath
            });
            // Allow access from valid auth flow referrer pages
            const isFromValidAuthFlowPage = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$securityRoutes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["VALID_AUTH_FLOW_REFERRERS"].some((prefix)=>referrerPath.startsWith(prefix));
            console.log('Auth flow referrer validation result:', {
                referrerPath: referrerPath,
                isFromValidAuthFlowPage: isFromValidAuthFlowPage,
                validReferrers: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$securityRoutes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["VALID_AUTH_FLOW_REFERRERS"]
            });
            return isFromValidAuthFlowPage;
        } catch (error) {
            console.warn('Error parsing referrer URL for auth flow validation:', error);
            return false;
        }
    };
    // Helper function to get appropriate fallback URL for direct access prevention (from centralized configuration)
    // Note: Using imported function from securityRoutes config
    if (clickPaidButton && isLoggedIn) {
        const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/checkout', req.url));
        response.cookies.set('click_paid_button', '', {
            maxAge: -1
        });
        return response;
    }
    // ✅ Handle security-check page access early (before other auth checks)
    // Allow both logged-in and non-logged-in users to access security-check
    if (pathname.startsWith('/security-check')) {
    // Skip other authentication checks and proceed to security-check specific logic below
    // This allows the page to handle both signup flow (non-logged-in) and account security (logged-in)
    } else {
        // Apply normal authentication checks for other routes
        if (authRoutes.some((route)=>pathname.startsWith(route))) {
            if (isLoggedIn) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/dashboard', req.url));
            }
        }
        if (flowProtectedRoutes.includes(pathname)) {
            if (!isLoggedIn && !authFlow) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/login', req.url));
            }
        }
        if (protectedPrefixes.some((prefix)=>pathname.startsWith(prefix))) {
            if (!isLoggedIn) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/login', req.url));
            }
        }
    }
    // ✅ Handle direct access prevention for security-check page
    if (pathname.startsWith('/security-check')) {
        // Check for direct URL access to security-check page
        const hasValidReferrer = hasValidNavigationReferrer(req);
        console.log('Security-check page access validation:', {
            pathname: pathname,
            referrer: req.headers.get('referer') || 'none',
            hasValidReferrer: hasValidReferrer
        });
        if (!hasValidReferrer) {
            // Direct access to security-check detected - redirect to safe fallback
            // Use different fallback based on authentication status
            const fallbackUrl = isLoggedIn ? '/account/overview' : '/signup';
            console.log('Direct access to security-check detected - redirecting to fallback:', {
                attemptedPath: pathname,
                fallbackUrl: fallbackUrl,
                isLoggedIn: isLoggedIn,
                referrer: req.headers.get('referer') || 'none'
            });
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL(fallbackUrl, req.url));
        }
        // Allow legitimate access to security-check page (proper referrer found)
        console.log('Legitimate access to security-check page allowed');
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    }
    // ✅ Handle referrer-based access control for auth flow protected routes
    if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$securityRoutes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["AUTH_FLOW_PROTECTED_ROUTES"].some((route)=>pathname.startsWith(route))) {
        // Check for direct URL access (referrer-based access control)
        const hasValidAuthReferrer = hasValidAuthFlowReferrer(req);
        console.log('Auth flow referrer validation result:', {
            pathname: pathname,
            referrer: req.headers.get('referer') || 'none',
            hasValidAuthReferrer: hasValidAuthReferrer
        });
        if (!hasValidAuthReferrer) {
            const fallbackUrl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$securityRoutes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["getAuthFlowFallbackUrl"])(pathname);
            console.log('Direct URL access to auth flow route detected - redirecting to fallback:', {
                attemptedPath: pathname,
                fallbackUrl: fallbackUrl,
                referrer: req.headers.get('referer') || 'none'
            });
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL(fallbackUrl, req.url));
        }
        // Allow legitimate access to auth flow route (proper referrer found)
        console.log('Legitimate access to auth flow route allowed:', pathname);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    }
    // ✅ Handle referrer-based access control and security verification for secure pages
    if (securityProtectedRoutes.some((route)=>pathname.startsWith(route))) {
        // Check for direct URL access (referrer-based access control)
        const hasValidReferrer = hasValidNavigationReferrer(req);
        console.log('Referrer validation result:', {
            pathname: pathname,
            referrer: req.headers.get('referer') || 'none',
            hasValidReferrer: hasValidReferrer
        });
        if (!hasValidReferrer) {
            const fallbackUrl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$securityRoutes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["getDirectAccessFallbackUrl"])(pathname);
            console.log('Direct URL access detected - redirecting to fallback:', {
                attemptedPath: pathname,
                fallbackUrl: fallbackUrl,
                referrer: req.headers.get('referer') || 'none'
            });
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL(fallbackUrl, req.url));
        }
        // Continue with normal security verification for legitimate navigation
        const securityVerified = req.cookies.get('security_verified');
        console.log('MIDDLEWARE: Security verification check:', {
            pathname,
            cookieExists: !!securityVerified,
            cookieValue: securityVerified?.value,
            isValid: securityVerified ? isValidSecurityCookie(securityVerified.value) : false,
            referrer: req.headers.get('referer') || 'none'
        });
        if (!securityVerified || !isValidSecurityCookie(securityVerified.value)) {
            console.log('Redirecting to security-check from:', pathname);
            // Use pathname + search params instead of full URL to avoid domain issues
            const fullPath = pathname + req.nextUrl.search;
            // Validate that the path is a valid secure route before including it in next parameter
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$securityRoutes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["isValidSecureRoute"])(fullPath)) {
                const nextUrl = encodeURIComponent(fullPath);
                console.log('Valid secure route, including next parameter:', fullPath);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL(`/security-check?next=${nextUrl}`, req.url));
            } else {
                console.warn('Invalid secure route detected, redirecting without next parameter:', fullPath);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/security-check', req.url));
            }
        }
        console.log('Security verification passed for:', pathname);
    }
    // ✅ Handle first login (signup)
    if (firstLogin && isLoggedIn) {
        const response = isFreeUser ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/pricing?source=signup_free_login_upgrade&feature=buy_trial', req.url)) : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/dashboard?source=signup_member_login_default', req.url));
        response.cookies.set('first_login', '', {
            maxAge: -1
        });
        return response;
    }
    if (returningLogin && isLoggedIn) {
        const response = isFreeUser ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/pricing?source=free_login_upgrade&feature=buy_trial', req.url)) : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/dashboard?source=member_login_default', req.url));
        response.cookies.set('returning_login', '', {
            maxAge: -1
        });
        return response;
    }
    if (pathname === '/pricing' && isLoggedIn) {
        if (sessionId) return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
        const hasSource = req.nextUrl.searchParams.get('source');
        if (!hasSource) {
            let source = 'default_pricing';
            if (ref === 'header') source = 'header_menu_pricing';
            else if (ref === 'footer') source = 'footer_menu_pricing';
            else if (ref === 'header_upgrade') source = 'header_upgrade_button';
            const prefix = isFreeUser ? 'free' : 'member';
            const redirectUrl = `/pricing?source=${prefix}_${source}&feature=buy_trial`;
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL(redirectUrl, req.url));
        }
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
}
const config = {
    matcher: [
        '/admin/:path*',
        '/user/:path*',
        '/pricing',
        '/change-password',
        '/account/:path*',
        '/dashboard/:path*',
        '/super-admin/:path*',
        '/not-found',
        '/security-check'
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__a8b25e96._.js.map