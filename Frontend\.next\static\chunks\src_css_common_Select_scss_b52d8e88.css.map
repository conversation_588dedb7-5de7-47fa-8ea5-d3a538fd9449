{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/css/common/Select.scss.css"], "sourcesContent": [":root{--font-g<PERSON>roy: \"<PERSON><PERSON>\", sans-serif}.common_select{margin-bottom:1.25rem}.common_select .select__control{font-size:1.25rem;min-height:56px;transition:none;box-shadow:none;border:none;background-color:rgba(0,0,0,0);padding:.625rem 1rem;border-radius:1rem;border:1px solid #666}@media(max-width: 1599px){.common_select .select__control{min-height:52px;font-size:1rem;padding:.625rem 1rem}}.common_select .select__control:hover,.common_select .select__control:focus{border-color:#666}.common_select .select__control .select__input-container{color:#fff}.common_select .select__control .select__input{opacity:0 !important}.common_select .select__control .select__placeholder{color:#fff}.common_select .select__control .select__value-container{padding-inline:0}.common_select .select__control .select__value-container .select__multi-value{background-color:#00adef;color:#fff}.common_select .select__control .select__value-container .select__multi-value .select__multi-value__label{color:#fff}.common_select .select__control .select__value-container--is-multi{flex-wrap:unset;overflow-x:auto;overflow-y:hidden}.common_select .select__control .select__value-container--is-multi .select__multi-value{min-width:max-content}.common_select .select__control .select__single-value{color:#fff;display:flex;align-items:center;gap:10px}.common_select .select__control .select__indicator-separator{display:none}.common_select .select__control .select__indicator{cursor:pointer;padding:0}.common_select .select__control .select__indicator svg{fill:#fff;width:24px;height:24px;transition:all ease-in-out .3s}.common_select .select__menu{width:100%;right:0;left:unset;background-color:#000;margin-bottom:0;margin-top:0}.common_select .select__menu .select__menu-notice{font-size:14px}.common_select .select__menu .select__menu-list .select__option{cursor:pointer;display:flex;align-items:center;gap:10px;font-size:16px;color:#fff}.common_select .select__menu .select__menu-list .select__option:hover{background-color:rgba(0,0,0,0);color:#00adef !important}.common_select .select__menu .select__menu-list .select__option.select__option--is-focused{background-color:rgba(0,0,0,0);color:#fff}.common_select .select__menu .select__menu-list .select__option.select__option--is-selected{background-color:rgba(0,0,0,0);color:#00adef !important}.common_select .select__control--menu-is-open .select__indicator svg{transform:rotate(-180deg)}"], "names": [], "mappings": "AAAA;;;;AAA0C;;;;AAAqC;;;;;;;;;;;;AAA2M;EAA0B;;;;;;;AAAqF;;;;AAA8F;;;;AAAoE;;;;AAAoE;;;;AAAgE;;;;AAA0E;;;;;AAAkH;;;;AAAqH;;;;;;AAAqH;;;;AAA8G;;;;;;;AAA0G;;;;AAA0E;;;;;AAA4E;;;;;;;AAAuH;;;;;;;;;AAA8G;;;;AAAiE;;;;;;;;;AAAkJ;;;;;AAA8H;;;;;AAAqI;;;;;AAAoJ"}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}