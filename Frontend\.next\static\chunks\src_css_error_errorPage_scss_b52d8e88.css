/* [project]/src/css/error/errorPage.scss.css [app-client] (css) */
@media (width <= 991px) {
  .w-md-100 {
    width: 100%;
  }

  .p-md-200 {
    padding: 0 200px;
  }
}

@media (width <= 700px) {
  .p-md-200 {
    padding: 0;
  }
}

.errorPage {
  height: calc(70vh - 90px);
  padding: 50px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-inline: 1rem;
}

.errorPage_inner {
  display: flex;
  justify-content: center;
  align-items: center;
}

@media (width <= 991px) {
  .errorPage_inner {
    flex-direction: column;
  }
}

.errorPage_inner figure {
  width: 300px;
}

@media (width <= 991px) {
  .errorPage_inner figure {
    width: 200px;
  }
}

.errorPage_content {
  padding-left: 2rem;
}

@media (width <= 991px) {
  .errorPage_content {
    padding-left: 0;
    text-align: center;
    padding-top: 2rem;
  }
}

.errorPage_content p {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

@media (width <= 991px) {
  .errorPage_content p {
    font-size: 1rem;
  }
}

.errorPage_content .commonSearch {
  position: relative;
}

@media (width <= 991px) {
  .errorPage_content .commonSearch {
    width: 100%;
    max-width: 400px;
    justify-content: center;
    margin: 0 auto;
  }

  .errorPage_content .commonSearch .form-control {
    width: 100%;
  }
}

.w-400px {
  width: 400px !important;
}

/*# sourceMappingURL=src_css_error_errorPage_scss_b52d8e88.css.map*/