(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/node_modules_cross-fetch_dist_browser-ponyfill_960ea50f.js", {

"[project]/node_modules/cross-fetch/dist/browser-ponyfill.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_cross-fetch_dist_browser-ponyfill_fba404a5.js",
  "static/chunks/node_modules_cross-fetch_dist_browser-ponyfill_8134bfbd.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/cross-fetch/dist/browser-ponyfill.js [app-client] (ecmascript)");
    });
});
}}),
}]);