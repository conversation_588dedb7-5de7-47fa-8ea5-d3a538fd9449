{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/css/common/CommonSearch.scss.css"], "sourcesContent": [":root{--font-gilroy: \"<PERSON><PERSON>\", sans-serif}.commonSearch{display:flex;position:relative;align-items:center}.commonSearch.searchBtn .form-control{padding:.5rem 1rem;background-color:#fff;color:#000;border:0;border-top-left-radius:10rem;border-bottom-left-radius:10rem;min-height:50px;width:calc(100% - 54px);min-width:auto;font-weight:600}.commonSearch.searchBtn .form-control::placeholder{color:#c5c5d5;opacity:1}.commonSearch.searchBtn .form-control:focus{box-shadow:none;outline:0;background-color:#fff;color:#000}.commonSearch .form-control{padding:.5rem 1rem;padding-left:50px;background-color:hsla(0,0%,100%,.3);color:#fff;border:0;border-radius:15px;min-height:70px;width:auto;width:400px;font-size:1.25rem;appearance:none;-webkit-appearance:none}@media(max-width: 991px){.commonSearch .form-control{font-size:16px;min-height:56px;padding-left:40px}}.commonSearch .form-control:hover{appearance:none}.commonSearch .form-control::placeholder{color:hsla(0,0%,100%,.8);opacity:1}.commonSearch .form-control:disabled{background-color:rgba(0,0,0,0)}.commonSearch .form-control:focus{box-shadow:none;outline:0;background-color:hsla(0,0%,100%,.3);color:#fff;border:0}.commonSearch .onlyIcon{position:absolute;left:20px;top:50%;transform:translateY(-50%);cursor:pointer}@media(max-width: 991px){.commonSearch .onlyIcon{left:15px}}.commonSearch .btnIcon{cursor:pointer;width:54px;min-height:50px;background-color:#00adef;border-top-right-radius:10rem;border-bottom-right-radius:10rem;display:flex;align-items:center;justify-content:center;transition:all ease-in-out .3s}.commonSearch .btnIcon:hover{background-color:#fea500}"], "names": [], "mappings": "AAAA;;;;AAA0C;;;;;;AAAgE;;;;;;;;;;;;;AAAuO;;;;;AAA2E;;;;;;;AAAuG;;;;;;;;;;;;;;;AAA6O;EAAyB;;;;;;;AAA8E;;;;AAAkD;;;;;AAA4E;;;;AAAoE;;;;;;;;AAAoH;;;;;;;;AAAsG;EAAyB;;;;;AAAmC;;;;;;;;;;;;;AAA+O"}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}