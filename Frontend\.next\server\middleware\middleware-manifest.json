{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_570db516._.js", "server/edge/chunks/[root of the server]__a8b25e96._.js", "server/edge/chunks/edge-wrapper_d6f08048.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/admin/:path*{(\\\\.json)}?", "originalSource": "/admin/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/user/:path*{(\\\\.json)}?", "originalSource": "/user/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/pricing{(\\\\.json)}?", "originalSource": "/pricing"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/change-password{(\\\\.json)}?", "originalSource": "/change-password"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/account/:path*{(\\\\.json)}?", "originalSource": "/account/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/super-admin/:path*{(\\\\.json)}?", "originalSource": "/super-admin/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/not-found{(\\\\.json)}?", "originalSource": "/not-found"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/security-check{(\\\\.json)}?", "originalSource": "/security-check"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JAdWpMZW7YX2HWGBSRb2tC6MF8pA5fg8t+vFkOc3uBA=", "__NEXT_PREVIEW_MODE_ID": "827ad1d1551527dac024e5855d975119", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5d056053dbf9aad5a5f0fbc0bf768d0de6400fab33517e42f1d95d1ab932c1ea", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5a904954e5074992297a940c810146af35b1d94fb48ec732cbacbd8e1adab032"}}}, "instrumentation": null, "functions": {}}