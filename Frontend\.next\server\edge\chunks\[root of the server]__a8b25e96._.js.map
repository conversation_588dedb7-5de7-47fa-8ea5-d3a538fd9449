{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/utils/environment.js"], "sourcesContent": ["/**\r\n * Environment Detection Utility\r\n * \r\n * Provides consistent environment detection and URL handling\r\n * across the frontend application for both client and server-side code.\r\n */\r\n\r\n/**\r\n * Detect the current environment\r\n * @returns {string} 'development' | 'production'\r\n */\r\nexport function getEnvironment() {\r\n  // Check NODE_ENV first\r\n  if (process.env.NODE_ENV === 'production') {\r\n    return 'production';\r\n  }\r\n  \r\n  // In browser, check the hostname\r\n  if (typeof window !== 'undefined') {\r\n    const hostname = window.location.hostname;\r\n    \r\n    // Production domains\r\n    if (hostname === 'tradereply.com' || hostname === 'www.tradereply.com' || hostname === 'dev.tradereply.com') {\r\n      return 'production';\r\n    }\r\n    \r\n    // Development domains\r\n    if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('.local')) {\r\n      return 'development';\r\n    }\r\n  }\r\n  \r\n  // Default to development for safety\r\n  return 'development';\r\n}\r\n\r\n/**\r\n * Get the current origin/base URL\r\n * @returns {string} The current origin URL\r\n */\r\nexport function getCurrentOrigin() {\r\n  // In browser environment\r\n  if (typeof window !== 'undefined') {\r\n    return window.location.origin;\r\n  }\r\n  \r\n  // In server-side environment, determine based on environment\r\n  const env = getEnvironment();\r\n  \r\n  if (env === 'production') {\r\n    // Check if we're on dev subdomain or main domain\r\n    if (process.env.NEXT_PUBLIC_API_BASE_URL?.includes('dev.tradereply.com')) {\r\n      return 'https://dev.tradereply.com';\r\n    }\r\n    return 'https://tradereply.com';\r\n  }\r\n  \r\n  // Development fallback\r\n  return 'http://localhost:3000';\r\n}\r\n\r\n/**\r\n * Get the appropriate API base URL\r\n * @returns {string} The API base URL\r\n */\r\nexport function getApiBaseUrl() {\r\n  // Use environment variable if available\r\n  if (process.env.NEXT_PUBLIC_API_BASE_URL) {\r\n    return process.env.NEXT_PUBLIC_API_BASE_URL;\r\n  }\r\n  \r\n  // Fallback based on environment\r\n  const env = getEnvironment();\r\n  \r\n  if (env === 'production') {\r\n    return 'https://dev.tradereply.com';\r\n  }\r\n  \r\n  return 'http://127.0.0.1:8000';\r\n}\r\n\r\n/**\r\n * Check if running in development mode\r\n * @returns {boolean} True if in development\r\n */\r\nexport function isDevelopment() {\r\n  return getEnvironment() === 'development';\r\n}\r\n\r\n/**\r\n * Check if running in production mode\r\n * @returns {boolean} True if in production\r\n */\r\nexport function isProduction() {\r\n  return getEnvironment() === 'production';\r\n}\r\n\r\n/**\r\n * Get environment-appropriate base URL for URL parsing\r\n * This is specifically for URL constructor usage\r\n * @returns {string} Base URL for URL constructor\r\n */\r\nexport function getBaseUrlForParsing() {\r\n  return getCurrentOrigin();\r\n}\r\n\r\n/**\r\n * Environment configuration object\r\n */\r\nexport const ENV_CONFIG = {\r\n  development: {\r\n    domains: ['localhost', '127.0.0.1', '::1', '.local', '.dev', '.test'],\r\n    defaultOrigin: 'http://localhost:3000',\r\n    defaultApiBase: 'http://127.0.0.1:8000',\r\n    secure: false\r\n  },\r\n  production: {\r\n    domains: ['tradereply.com', 'dev.tradereply.com', 'www.tradereply.com'],\r\n    defaultOrigin: 'https://dev.tradereply.com',\r\n    defaultApiBase: 'https://dev.tradereply.com',\r\n    secure: true\r\n  }\r\n};\r\n\r\n/**\r\n * Get configuration for current environment\r\n * @returns {object} Environment configuration\r\n */\r\nexport function getEnvConfig() {\r\n  const env = getEnvironment();\r\n  return ENV_CONFIG[env];\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED;;;CAGC;;;;;;;;;;AACM,SAAS;IACd,uBAAuB;IACvB,uCAA2C;;IAE3C;IAEA,iCAAiC;IACjC,uCAAmC;;IAYnC;IAEA,oCAAoC;IACpC,OAAO;AACT;AAMO,SAAS;IACd,yBAAyB;IACzB,uCAAmC;;IAEnC;IAEA,6DAA6D;IAC7D,MAAM,MAAM;IAEZ,IAAI,QAAQ,cAAc;QACxB,iDAAiD;QACjD,+DAA0C,SAAS,uBAAuB;YACxE,OAAO;QACT;QACA,OAAO;IACT;IAEA,uBAAuB;IACvB,OAAO;AACT;AAMO,SAAS;IACd,wCAAwC;IACxC,wCAA0C;QACxC;IACF;;IAEA,gCAAgC;IAChC,MAAM;AAOR;AAMO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAMO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAOO,SAAS;IACd,OAAO;AACT;AAKO,MAAM,aAAa;IACxB,aAAa;QACX,SAAS;YAAC;YAAa;YAAa;YAAO;YAAU;YAAQ;SAAQ;QACrE,eAAe;QACf,gBAAgB;QAChB,QAAQ;IACV;IACA,YAAY;QACV,SAAS;YAAC;YAAkB;YAAsB;SAAqB;QACvE,eAAe;QACf,gBAAgB;QAChB,QAAQ;IACV;AACF;AAMO,SAAS;IACd,MAAM,MAAM;IACZ,OAAO,UAAU,CAAC,IAAI;AACxB"}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/config/securityRoutes.js"], "sourcesContent": ["/**\r\n * Security Routes Configuration\r\n *\r\n * This file contains the security-protected routes configuration that is used\r\n * by the Next.js middleware and other frontend components. This configuration\r\n * is self-contained within the frontend to avoid dependencies on backend APIs.\r\n *\r\n * To update this configuration:\r\n * 1. Modify the constants below directly in this file\r\n * 2. Update the corresponding backend config/security.php if needed for backend middleware\r\n * 3. Redeploy the frontend\r\n *\r\n * Note: This is the single source of truth for frontend security route configuration.\r\n */\r\n\r\n/**\r\n * Security-protected routes that require verification\r\n * Add new routes here when implementing new secure pages\r\n */\r\nexport const SECURITY_PROTECTED_ROUTES = [\r\n  '/account/phone/setup',\r\n  '/account/email/setup',\r\n  // '/account/password/change',\r\n  // '/account/2fa/setup',\r\n  '/account/security/two-factor',\r\n  '/account/username/setup',\r\n  '/account/address/manage',\r\n  '/account/address/setup',\r\n];\r\n\r\n/**\r\n * Auth flow protected routes that require proper signup flow navigation\r\n * These routes should only be accessible through the legitimate signup process\r\n */\r\nexport const AUTH_FLOW_PROTECTED_ROUTES = [\r\n  '/create-username',\r\n  '/change-password'\r\n];\r\n\r\n/**\r\n * Valid referrer path prefixes (pages users can navigate from)\r\n * These are the application pages that users can legitimately navigate from to secure pages\r\n */\r\nexport const VALID_REFERRER_PREFIXES = [\r\n  '/account',\r\n  '/dashboard',\r\n  '/user',\r\n  '/marketplace',\r\n  '/pricing',\r\n  '/help',\r\n  '/settings',\r\n  '/', // Home page\r\n];\r\n\r\n/**\r\n * Invalid referrer paths (pages users should not navigate from)\r\n * These are pages that should not be able to directly link to secure pages\r\n */\r\nexport const INVALID_REFERRER_PATHS = [\r\n  '/login',\r\n  '/signup',\r\n  '/forget-password',\r\n  '/verify-email',\r\n  '/security-check',\r\n  '/logout',\r\n];\r\n\r\n/**\r\n * Fallback URL mappings for direct access prevention\r\n * When users try to access secure pages directly, they are redirected to these fallback URLs\r\n */\r\nexport const FALLBACK_URL_MAPPINGS = {\r\n  '/account/phone/setup': '/account/details',\r\n  '/account/email/setup': '/account/details',\r\n  // '/account/password/change': '/account/overview',\r\n  // '/account/2fa/setup': '/account/overview',\r\n  '/account/security/two-factor': '/account/security',\r\n  '/account/username/setup': '/account/details',\r\n  '/account/address/manage': '/account/details',\r\n  '/account/address/setup': '/account/address/manage',\r\n};\r\n\r\n/**\r\n * Default fallback URL when no specific mapping exists\r\n */\r\nexport const DEFAULT_FALLBACK_URL = '/account/overview';\r\n\r\n/**\r\n * Valid referrer prefixes for security-check page access\r\n * These are the pages that can legitimately redirect users to the security-check page\r\n */\r\nexport const VALID_SECURITY_CHECK_REFERRERS = [\r\n  '/account',\r\n  '/dashboard',\r\n  '/user'\r\n];\r\n\r\n/**\r\n * Valid referrer prefixes for auth flow protected routes\r\n * These are the pages that can legitimately redirect users to auth flow pages like /create-username\r\n */\r\nexport const VALID_AUTH_FLOW_REFERRERS = [\r\n  '/security-check',\r\n];\r\n\r\n/**\r\n * Security configuration settings\r\n */\r\nexport const SECURITY_CONFIG = {\r\n  // Enable referrer-based access control\r\n  referrerControlEnabled: true,\r\n\r\n  // Cookie configuration for client-side validation\r\n  cookie: {\r\n    name: 'security_verified',\r\n    // Note: Actual expiration is controlled by backend, this is for client-side validation only\r\n    defaultExpirationMinutes: 5,\r\n    checkIntervalSeconds: 30,\r\n  },\r\n};\r\n\r\nimport { getBaseUrlForParsing } from '@/utils/environment';\r\n\r\n/**\r\n * Helper function to validate if a path is a valid secure route\r\n * @param {string} path - The path to validate\r\n * @returns {boolean} True if the path is a valid secure route\r\n */\r\nexport function isValidSecureRoute(path) {\r\n  try {\r\n    // Decode URL if it's encoded\r\n    const decodedPath = decodeURIComponent(path);\r\n\r\n    // For relative paths, extract path directly without URL constructor\r\n    let pathOnly;\r\n    if (decodedPath.startsWith('/')) {\r\n      // It's already a relative path, use it directly\r\n      pathOnly = decodedPath.split('?')[0]; // Remove query parameters\r\n    } else {\r\n      // It might be an absolute URL, use URL constructor with dynamic base\r\n      const baseUrl = getBaseUrlForParsing();\r\n      const url = new URL(decodedPath, baseUrl);\r\n      pathOnly = url.pathname;\r\n    }\r\n\r\n    // Check if the path exactly matches any of the protected routes\r\n    // or if it starts with any of the protected routes (for sub-paths)\r\n    return SECURITY_PROTECTED_ROUTES.some(route => {\r\n      // Exact match\r\n      if (pathOnly === route) {\r\n        return true;\r\n      }\r\n\r\n      // Check if path starts with the route (for sub-paths like /account/phone/setup/step2)\r\n      if (pathOnly.startsWith(route + '/')) {\r\n        return true;\r\n      }\r\n\r\n      return false;\r\n    });\r\n  } catch (error) {\r\n    console.warn('Error validating secure route:', error);\r\n    return false;\r\n  }\r\n}\r\n\r\n/**\r\n * Helper function to get appropriate fallback URL for direct access prevention\r\n * @param {string} securePagePath - The secure page path\r\n * @returns {string} Fallback URL\r\n */\r\nexport function getDirectAccessFallbackUrl(securePagePath) {\r\n  return FALLBACK_URL_MAPPINGS[securePagePath] || DEFAULT_FALLBACK_URL;\r\n}\r\n\r\n/**\r\n * Helper function to validate if a path is a valid auth flow route\r\n * @param {string} path - The path to validate\r\n * @returns {boolean} True if the path is a valid auth flow route\r\n */\r\nexport function isValidAuthFlowRoute(path) {\r\n  try {\r\n    // Decode URL if it's encoded\r\n    const decodedPath = decodeURIComponent(path);\r\n\r\n    // For relative paths, extract path directly without URL constructor\r\n    let pathOnly;\r\n    if (decodedPath.startsWith('/')) {\r\n      // It's already a relative path, use it directly\r\n      pathOnly = decodedPath.split('?')[0]; // Remove query parameters\r\n    } else {\r\n      // It might be an absolute URL, use URL constructor with dynamic base\r\n      const baseUrl = getBaseUrlForParsing();\r\n      const url = new URL(decodedPath, baseUrl);\r\n      pathOnly = url.pathname;\r\n    }\r\n\r\n    // Check if the path exactly matches any of the auth flow protected routes\r\n    return AUTH_FLOW_PROTECTED_ROUTES.some(route => {\r\n      // Exact match\r\n      if (pathOnly === route) {\r\n        return true;\r\n      }\r\n\r\n      // Check if path starts with the route (for sub-paths)\r\n      if (pathOnly.startsWith(route + '/')) {\r\n        return true;\r\n      }\r\n\r\n      return false;\r\n    });\r\n  } catch (error) {\r\n    console.warn('Error validating auth flow route:', error);\r\n    return false;\r\n  }\r\n}\r\n\r\n/**\r\n * Get the appropriate fallback URL for auth flow direct access prevention\r\n * @param {string} attemptedPath - The path the user tried to access directly\r\n * @returns {string} - The fallback URL to redirect to\r\n */\r\nexport function getAuthFlowFallbackUrl(attemptedPath) {\r\n  // Map specific auth flow routes to their appropriate fallback pages\r\n  const authFlowFallbacks = {\r\n    '/create-username': '/login',\r\n    // Add more auth flow fallbacks as needed\r\n  };\r\n\r\n  // Check for specific route fallback first\r\n  if (authFlowFallbacks[attemptedPath]) {\r\n    return authFlowFallbacks[attemptedPath];\r\n  }\r\n\r\n  // Default fallback for auth flow routes\r\n  return '/login';\r\n}\r\n\r\n/**\r\n * Configuration metadata\r\n */\r\nexport const CONFIG_METADATA = {\r\n  lastSynced: new Date().toISOString(),\r\n  source: 'backend:config/security.php',\r\n  version: '1.0.0'\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;CAaC,GAED;;;CAGC;;;;;;;;;;;;;;;;AAuGD;AAtGO,MAAM,4BAA4B;IACvC;IACA;IACA,8BAA8B;IAC9B,wBAAwB;IACxB;IACA;IACA;IACA;CACD;AAMM,MAAM,6BAA6B;IACxC;IACA;CACD;AAMM,MAAM,0BAA0B;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAMM,MAAM,yBAAyB;IACpC;IACA;IACA;IACA;IACA;IACA;CACD;AAMM,MAAM,wBAAwB;IACnC,wBAAwB;IACxB,wBAAwB;IACxB,mDAAmD;IACnD,6CAA6C;IAC7C,gCAAgC;IAChC,2BAA2B;IAC3B,2BAA2B;IAC3B,0BAA0B;AAC5B;AAKO,MAAM,uBAAuB;AAM7B,MAAM,iCAAiC;IAC5C;IACA;IACA;CACD;AAMM,MAAM,4BAA4B;IACvC;CACD;AAKM,MAAM,kBAAkB;IAC7B,uCAAuC;IACvC,wBAAwB;IAExB,kDAAkD;IAClD,QAAQ;QACN,MAAM;QACN,4FAA4F;QAC5F,0BAA0B;QAC1B,sBAAsB;IACxB;AACF;;AASO,SAAS,mBAAmB,IAAI;IACrC,IAAI;QACF,6BAA6B;QAC7B,MAAM,cAAc,mBAAmB;QAEvC,oEAAoE;QACpE,IAAI;QACJ,IAAI,YAAY,UAAU,CAAC,MAAM;YAC/B,gDAAgD;YAChD,WAAW,YAAY,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,0BAA0B;QAClE,OAAO;YACL,qEAAqE;YACrE,MAAM,UAAU,CAAA,GAAA,mIAAA,CAAA,uBAAoB,AAAD;YACnC,MAAM,MAAM,IAAI,IAAI,aAAa;YACjC,WAAW,IAAI,QAAQ;QACzB;QAEA,gEAAgE;QAChE,mEAAmE;QACnE,OAAO,0BAA0B,IAAI,CAAC,CAAA;YACpC,cAAc;YACd,IAAI,aAAa,OAAO;gBACtB,OAAO;YACT;YAEA,sFAAsF;YACtF,IAAI,SAAS,UAAU,CAAC,QAAQ,MAAM;gBACpC,OAAO;YACT;YAEA,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,kCAAkC;QAC/C,OAAO;IACT;AACF;AAOO,SAAS,2BAA2B,cAAc;IACvD,OAAO,qBAAqB,CAAC,eAAe,IAAI;AAClD;AAOO,SAAS,qBAAqB,IAAI;IACvC,IAAI;QACF,6BAA6B;QAC7B,MAAM,cAAc,mBAAmB;QAEvC,oEAAoE;QACpE,IAAI;QACJ,IAAI,YAAY,UAAU,CAAC,MAAM;YAC/B,gDAAgD;YAChD,WAAW,YAAY,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,0BAA0B;QAClE,OAAO;YACL,qEAAqE;YACrE,MAAM,UAAU,CAAA,GAAA,mIAAA,CAAA,uBAAoB,AAAD;YACnC,MAAM,MAAM,IAAI,IAAI,aAAa;YACjC,WAAW,IAAI,QAAQ;QACzB;QAEA,0EAA0E;QAC1E,OAAO,2BAA2B,IAAI,CAAC,CAAA;YACrC,cAAc;YACd,IAAI,aAAa,OAAO;gBACtB,OAAO;YACT;YAEA,sDAAsD;YACtD,IAAI,SAAS,UAAU,CAAC,QAAQ,MAAM;gBACpC,OAAO;YACT;YAEA,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,qCAAqC;QAClD,OAAO;IACT;AACF;AAOO,SAAS,uBAAuB,aAAa;IAClD,oEAAoE;IACpE,MAAM,oBAAoB;QACxB,oBAAoB;IAEtB;IAEA,0CAA0C;IAC1C,IAAI,iBAAiB,CAAC,cAAc,EAAE;QACpC,OAAO,iBAAiB,CAAC,cAAc;IACzC;IAEA,wCAAwC;IACxC,OAAO;AACT;AAKO,MAAM,kBAAkB;IAC7B,YAAY,IAAI,OAAO,WAAW;IAClC,QAAQ;IACR,SAAS;AACX"}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\r\nimport {\r\n  SECURITY_PROTECTED_ROUTES,\r\n  AUTH_FLOW_PROTECTED_ROUTES,\r\n  VALID_REFERRER_PREFIXES,\r\n  INVALID_REFERRER_PATHS,\r\n  VALID_SECURITY_CHECK_REFERRERS,\r\n  VALID_AUTH_FLOW_REFERRERS,\r\n  isValidSecureRoute,\r\n  isValidAuthFlowRoute,\r\n  getDirectAccessFallbackUrl,\r\n  getAuthFlowFallbackUrl\r\n} from '@/config/securityRoutes';\r\n\r\n/**\r\n * Validate security verification cookie\r\n */\r\nfunction isValidSecurityCookie(cookieValue) {\r\n  if (!cookieValue) {\r\n    return false;\r\n  }\r\n\r\n  // Simple validation for 'true' value (legacy)\r\n  if (cookieValue === 'true') {\r\n    return true;\r\n  }\r\n\r\n  // Simple validation for 'verified' value (fallback)\r\n  if (cookieValue === 'verified') {\r\n    return true;\r\n  }\r\n\r\n  // Check if it's a session token (64 character hex string)\r\n  if (typeof cookieValue === 'string' && cookieValue.length === 64 && /^[a-f0-9]+$/i.test(cookieValue)) {\r\n    // For session tokens, we assume they're valid on client side\r\n    // Backend will do the authoritative validation\r\n    return true;\r\n  }\r\n\r\n  // Check if it's a UUID format (with hyphens)\r\n  if (typeof cookieValue === 'string' && /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i.test(cookieValue)) {\r\n    // For UUID session tokens, we assume they're valid on client side\r\n    // Backend will do the authoritative validation\r\n    return true;\r\n  }\r\n\r\n  // Validate old encrypted payload format for backward compatibility\r\n  try {\r\n    const payload = JSON.parse(atob(cookieValue));\r\n\r\n    if (payload.verified_at) {\r\n      const verifiedAt = new Date(payload.verified_at);\r\n      const now = new Date();\r\n      const diffInSeconds = (now.getTime() - verifiedAt.getTime()) / 1000;\r\n\r\n      // Check if within reasonable window (backend will do authoritative validation)\r\n      // Use 15 minutes as a safe buffer since backend config may vary\r\n      return diffInSeconds <= 900;\r\n    }\r\n  } catch (e) {\r\n    // Invalid cookie format\r\n    return false;\r\n  }\r\n\r\n  return false;\r\n}\r\n\r\nexport function middleware(req) {\r\n  const token = req.cookies.get('authToken');\r\n  const authFlow = req.cookies.get('auth_flow_verified');\r\n  const pathname = req.nextUrl.pathname;\r\n  const ref = req.nextUrl.searchParams.get('ref');\r\n  const sessionId = req.nextUrl.searchParams.get('session_id');\r\n\r\n  const activeSubscription = req.cookies.get('active_subscription');\r\n  const isFreeUser = !activeSubscription || activeSubscription?.plan?.billing_type === 'free';\r\n  const isLoggedIn = !!token;\r\n\r\n  const firstLogin = req.cookies.get('first_login')?.value || req.cookies.get('first_login');\r\n  const returningLogin = req.cookies.get('returning_login')?.value || req.cookies.get('returning_login');\r\n  const clickPaidButton = req.cookies.get('click_paid_button')?.value || req.cookies.get('click_paid_button');\r\n\r\n  const authRoutes = ['/login', '/signup', '/forget-password', '/verify-email', '/home', '/create-username'];\r\n  const protectedPrefixes = ['/user', '/account', '/dashboard', '/super-admin', '/not-found'];\r\n  const flowProtectedRoutes = ['/change-password'];\r\n\r\n  // ✅ Security verification protected pages (from centralized configuration)\r\n  const securityProtectedRoutes = SECURITY_PROTECTED_ROUTES;\r\n\r\n  // Helper function to validate if a path is a valid secure route (from centralized configuration)\r\n  // Note: Using imported function from securityRoutes config\r\n\r\n  // Helper function to check if user arrived via legitimate navigation (not direct URL access)\r\n  const hasValidNavigationReferrer = (request) => {\r\n    const referrer = request.headers.get('referer');\r\n    const currentPath = request.nextUrl.pathname;\r\n\r\n    // No referrer indicates direct URL access (typed in browser, bookmark, etc.)\r\n    if (!referrer) {\r\n      return false;\r\n    }\r\n\r\n    // Special handling for security-check page access\r\n    if (currentPath.startsWith('/security-check')) {\r\n      // For security-check page, only allow access from secure pages or middleware redirects\r\n      try {\r\n        const referrerUrl = new URL(referrer);\r\n        const referrerPath = referrerUrl.pathname;\r\n\r\n        // ✅ Special case: Allow access from signup page when signup parameter is present\r\n        const isSignupFlow = req.nextUrl.searchParams.has('signup') && referrerPath === '/signup';\r\n\r\n        // Allow access from secure pages (when middleware redirects users to security-check)\r\n        const isFromSecurePage = securityProtectedRoutes.some(route => referrerPath.startsWith(route));\r\n\r\n        // Allow access from valid application pages that might trigger security verification\r\n        const validSecurityCheckReferrers = VALID_SECURITY_CHECK_REFERRERS;\r\n        const isFromValidPage = validSecurityCheckReferrers.some(prefix => referrerPath.startsWith(prefix));\r\n\r\n        console.log('Security-check referrer validation:', {\r\n          referrerPath: referrerPath,\r\n          isFromSecurePage: isFromSecurePage,\r\n          isFromValidPage: isFromValidPage,\r\n          isSignupFlow: isSignupFlow\r\n        });\r\n\r\n        return isFromSecurePage || isFromValidPage || isSignupFlow;\r\n      } catch (error) {\r\n        console.warn('Error parsing referrer URL for security-check:', error);\r\n        return false;\r\n      }\r\n    }\r\n\r\n    // Special case: Allow referrers from security-check page (after successful verification)\r\n    try {\r\n      const referrerUrl = new URL(referrer);\r\n      if (referrerUrl.pathname.startsWith('/security-check')) {\r\n        console.log('MIDDLEWARE: Allowing navigation from security-check page:', {\r\n          referrer: referrer,\r\n          referrerPath: referrerUrl.pathname,\r\n          currentPath: currentPath\r\n        });\r\n        return true; // Allow navigation from security-check page\r\n      }\r\n    } catch (error) {\r\n      console.warn('MIDDLEWARE: Error parsing referrer URL:', error);\r\n      // Continue with normal validation if URL parsing fails\r\n    }\r\n\r\n    // Validate that referrer is from a valid application page\r\n    return isValidApplicationReferrer(referrer, request);\r\n  };\r\n\r\n  // Helper function to check if the referrer is from a valid application page\r\n  const isValidApplicationReferrer = (referrer, request) => {\r\n    try {\r\n      const parsedUrl = new URL(referrer);\r\n\r\n      // Domain comparison logic removed to ensure consistent behavior across environments\r\n      \r\n      // Get the referrer path\r\n      const referrerPath = parsedUrl.pathname;\r\n\r\n      // Don't allow referrers from invalid pages (from centralized configuration)\r\n      // Note: /security-check is handled specially in hasValidNavigationReferrer\r\n      if (INVALID_REFERRER_PATHS.some(invalidPage => referrerPath.startsWith(invalidPage))) {\r\n        return false;\r\n      }\r\n\r\n      // Allow referrers from valid application pages (from centralized configuration)\r\n      return VALID_REFERRER_PREFIXES.some(validPrefix => referrerPath.startsWith(validPrefix));\r\n    } catch (error) {\r\n      console.warn('Error validating referrer:', error);\r\n      return false;\r\n    }\r\n  };\r\n\r\n  // Helper function to validate auth flow referrers\r\n  const hasValidAuthFlowReferrer = (req) => {\r\n    const referrer = req.headers.get('referer');\r\n    const currentPath = req.nextUrl.pathname;\r\n\r\n    if (!referrer) {\r\n      console.log('Auth flow referrer validation: No referrer found');\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      const referrerUrl = new URL(referrer);\r\n      const referrerPath = referrerUrl.pathname;\r\n\r\n      console.log('Auth flow referrer validation:', {\r\n        currentPath: currentPath,\r\n        referrer: referrer,\r\n        referrerPath: referrerPath\r\n      });\r\n\r\n      // Allow access from valid auth flow referrer pages\r\n      const isFromValidAuthFlowPage = VALID_AUTH_FLOW_REFERRERS.some(prefix => referrerPath.startsWith(prefix));\r\n\r\n      console.log('Auth flow referrer validation result:', {\r\n        referrerPath: referrerPath,\r\n        isFromValidAuthFlowPage: isFromValidAuthFlowPage,\r\n        validReferrers: VALID_AUTH_FLOW_REFERRERS\r\n      });\r\n\r\n      return isFromValidAuthFlowPage;\r\n    } catch (error) {\r\n      console.warn('Error parsing referrer URL for auth flow validation:', error);\r\n      return false;\r\n    }\r\n  };\r\n\r\n  // Helper function to get appropriate fallback URL for direct access prevention (from centralized configuration)\r\n  // Note: Using imported function from securityRoutes config\r\n\r\n  if (clickPaidButton && isLoggedIn) {\r\n    const response = NextResponse.redirect(new URL('/checkout', req.url));\r\n    response.cookies.set('click_paid_button', '', { maxAge: -1 });\r\n    return response;\r\n  }\r\n\r\n  // ✅ Handle security-check page access early (before other auth checks)\r\n  // Allow both logged-in and non-logged-in users to access security-check\r\n  if (pathname.startsWith('/security-check')) {\r\n    // Skip other authentication checks and proceed to security-check specific logic below\r\n    // This allows the page to handle both signup flow (non-logged-in) and account security (logged-in)\r\n  } else {\r\n    // Apply normal authentication checks for other routes\r\n    if (authRoutes.some((route) => pathname.startsWith(route))) {\r\n      if (isLoggedIn) {\r\n        return NextResponse.redirect(new URL('/dashboard', req.url));\r\n      }\r\n    }\r\n\r\n    if (flowProtectedRoutes.includes(pathname)) {\r\n      if (!isLoggedIn && !authFlow) {\r\n        return NextResponse.redirect(new URL('/login', req.url));\r\n      }\r\n    }\r\n\r\n    if (protectedPrefixes.some((prefix) => pathname.startsWith(prefix))) {\r\n      if (!isLoggedIn) {\r\n        return NextResponse.redirect(new URL('/login', req.url));\r\n      }\r\n    }\r\n  }\r\n\r\n  // ✅ Handle direct access prevention for security-check page\r\n  if (pathname.startsWith('/security-check')) {\r\n    // Check for direct URL access to security-check page\r\n    const hasValidReferrer = hasValidNavigationReferrer(req);\r\n\r\n    console.log('Security-check page access validation:', {\r\n      pathname: pathname,\r\n      referrer: req.headers.get('referer') || 'none',\r\n      hasValidReferrer: hasValidReferrer\r\n    });\r\n\r\n    if (!hasValidReferrer) {\r\n      // Direct access to security-check detected - redirect to safe fallback\r\n      // Use different fallback based on authentication status\r\n      const fallbackUrl = isLoggedIn ? '/account/overview' : '/signup';\r\n\r\n      console.log('Direct access to security-check detected - redirecting to fallback:', {\r\n        attemptedPath: pathname,\r\n        fallbackUrl: fallbackUrl,\r\n        isLoggedIn: isLoggedIn,\r\n        referrer: req.headers.get('referer') || 'none'\r\n      });\r\n\r\n      return NextResponse.redirect(new URL(fallbackUrl, req.url));\r\n    }\r\n\r\n    // Allow legitimate access to security-check page (proper referrer found)\r\n    console.log('Legitimate access to security-check page allowed');\r\n    return NextResponse.next();\r\n  }\r\n\r\n  // ✅ Handle referrer-based access control for auth flow protected routes\r\n  if (AUTH_FLOW_PROTECTED_ROUTES.some((route) => pathname.startsWith(route))) {\r\n\r\n    // Check for direct URL access (referrer-based access control)\r\n    const hasValidAuthReferrer = hasValidAuthFlowReferrer(req);\r\n    console.log('Auth flow referrer validation result:', {\r\n      pathname: pathname,\r\n      referrer: req.headers.get('referer') || 'none',\r\n      hasValidAuthReferrer: hasValidAuthReferrer\r\n    });\r\n\r\n    if (!hasValidAuthReferrer) {\r\n      const fallbackUrl = getAuthFlowFallbackUrl(pathname);\r\n\r\n      console.log('Direct URL access to auth flow route detected - redirecting to fallback:', {\r\n        attemptedPath: pathname,\r\n        fallbackUrl: fallbackUrl,\r\n        referrer: req.headers.get('referer') || 'none'\r\n      });\r\n\r\n      return NextResponse.redirect(new URL(fallbackUrl, req.url));\r\n    }\r\n\r\n    // Allow legitimate access to auth flow route (proper referrer found)\r\n    console.log('Legitimate access to auth flow route allowed:', pathname);\r\n    return NextResponse.next();\r\n  }\r\n\r\n  // ✅ Handle referrer-based access control and security verification for secure pages\r\n  if (securityProtectedRoutes.some((route) => pathname.startsWith(route))) {\r\n\r\n    // Check for direct URL access (referrer-based access control)\r\n    const hasValidReferrer = hasValidNavigationReferrer(req);\r\n    console.log('Referrer validation result:', {\r\n      pathname: pathname,\r\n      referrer: req.headers.get('referer') || 'none',\r\n      hasValidReferrer: hasValidReferrer\r\n    });\r\n\r\n    if (!hasValidReferrer) {\r\n      const fallbackUrl = getDirectAccessFallbackUrl(pathname);\r\n\r\n      console.log('Direct URL access detected - redirecting to fallback:', {\r\n        attemptedPath: pathname,\r\n        fallbackUrl: fallbackUrl,\r\n        referrer: req.headers.get('referer') || 'none'\r\n      });\r\n\r\n      return NextResponse.redirect(new URL(fallbackUrl, req.url));\r\n    }\r\n\r\n    // Continue with normal security verification for legitimate navigation\r\n    const securityVerified = req.cookies.get('security_verified');\r\n\r\n    console.log('MIDDLEWARE: Security verification check:', {\r\n      pathname,\r\n      cookieExists: !!securityVerified,\r\n      cookieValue: securityVerified?.value,\r\n      isValid: securityVerified ? isValidSecurityCookie(securityVerified.value) : false,\r\n      referrer: req.headers.get('referer') || 'none'\r\n    });\r\n\r\n    if (!securityVerified || !isValidSecurityCookie(securityVerified.value)) {\r\n      console.log('Redirecting to security-check from:', pathname);\r\n      // Use pathname + search params instead of full URL to avoid domain issues\r\n      const fullPath = pathname + req.nextUrl.search;\r\n\r\n      // Validate that the path is a valid secure route before including it in next parameter\r\n      if (isValidSecureRoute(fullPath)) {\r\n        const nextUrl = encodeURIComponent(fullPath);\r\n        console.log('Valid secure route, including next parameter:', fullPath);\r\n        return NextResponse.redirect(new URL(`/security-check?next=${nextUrl}`, req.url));\r\n      } else {\r\n        console.warn('Invalid secure route detected, redirecting without next parameter:', fullPath);\r\n        return NextResponse.redirect(new URL('/security-check', req.url));\r\n      }\r\n    }\r\n\r\n    console.log('Security verification passed for:', pathname);\r\n  }\r\n\r\n\r\n  // ✅ Handle first login (signup)\r\n\r\n  if (firstLogin && isLoggedIn) {\r\n    const response = isFreeUser\r\n      ? NextResponse.redirect(new URL('/pricing?source=signup_free_login_upgrade&feature=buy_trial', req.url))\r\n      : NextResponse.redirect(new URL('/dashboard?source=signup_member_login_default', req.url));\r\n    response.cookies.set('first_login', '', { maxAge: -1 });\r\n    return response;\r\n  }\r\n\r\n  if (returningLogin && isLoggedIn) {\r\n    const response = isFreeUser\r\n      ? NextResponse.redirect(new URL('/pricing?source=free_login_upgrade&feature=buy_trial', req.url))\r\n      : NextResponse.redirect(new URL('/dashboard?source=member_login_default', req.url));\r\n    response.cookies.set('returning_login', '', { maxAge: -1 });\r\n    return response;\r\n  }\r\n\r\n  if (pathname === '/pricing' && isLoggedIn) {\r\n    if (sessionId) return NextResponse.next();\r\n\r\n    const hasSource = req.nextUrl.searchParams.get('source');\r\n    if (!hasSource) {\r\n      let source = 'default_pricing';\r\n      if (ref === 'header') source = 'header_menu_pricing';\r\n      else if (ref === 'footer') source = 'footer_menu_pricing';\r\n      else if (ref === 'header_upgrade') source = 'header_upgrade_button';\r\n\r\n      const prefix = isFreeUser ? 'free' : 'member';\r\n      const redirectUrl = `/pricing?source=${prefix}_${source}&feature=buy_trial`;\r\n\r\n      return NextResponse.redirect(new URL(redirectUrl, req.url));\r\n    }\r\n  }\r\n\r\n  return NextResponse.next();\r\n}\r\n\r\nexport const config = {\r\n  matcher: [\r\n    '/admin/:path*',\r\n    '/user/:path*',\r\n    '/pricing',\r\n    '/change-password',\r\n    '/account/:path*',\r\n    '/dashboard/:path*',\r\n    '/super-admin/:path*',\r\n    '/not-found',\r\n    '/security-check',\r\n  ],\r\n\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAaA;;CAEC,GACD,SAAS,sBAAsB,WAAW;IACxC,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IAEA,8CAA8C;IAC9C,IAAI,gBAAgB,QAAQ;QAC1B,OAAO;IACT;IAEA,oDAAoD;IACpD,IAAI,gBAAgB,YAAY;QAC9B,OAAO;IACT;IAEA,0DAA0D;IAC1D,IAAI,OAAO,gBAAgB,YAAY,YAAY,MAAM,KAAK,MAAM,eAAe,IAAI,CAAC,cAAc;QACpG,6DAA6D;QAC7D,+CAA+C;QAC/C,OAAO;IACT;IAEA,6CAA6C;IAC7C,IAAI,OAAO,gBAAgB,YAAY,kEAAkE,IAAI,CAAC,cAAc;QAC1H,kEAAkE;QAClE,+CAA+C;QAC/C,OAAO;IACT;IAEA,mEAAmE;IACnE,IAAI;QACF,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK;QAEhC,IAAI,QAAQ,WAAW,EAAE;YACvB,MAAM,aAAa,IAAI,KAAK,QAAQ,WAAW;YAC/C,MAAM,MAAM,IAAI;YAChB,MAAM,gBAAgB,CAAC,IAAI,OAAO,KAAK,WAAW,OAAO,EAAE,IAAI;YAE/D,+EAA+E;YAC/E,gEAAgE;YAChE,OAAO,iBAAiB;QAC1B;IACF,EAAE,OAAO,GAAG;QACV,wBAAwB;QACxB,OAAO;IACT;IAEA,OAAO;AACT;AAEO,SAAS,WAAW,GAAG;IAC5B,MAAM,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC;IAC9B,MAAM,WAAW,IAAI,OAAO,CAAC,GAAG,CAAC;IACjC,MAAM,WAAW,IAAI,OAAO,CAAC,QAAQ;IACrC,MAAM,MAAM,IAAI,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC;IACzC,MAAM,YAAY,IAAI,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC;IAE/C,MAAM,qBAAqB,IAAI,OAAO,CAAC,GAAG,CAAC;IAC3C,MAAM,aAAa,CAAC,sBAAsB,oBAAoB,MAAM,iBAAiB;IACrF,MAAM,aAAa,CAAC,CAAC;IAErB,MAAM,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC;IAC5E,MAAM,iBAAiB,IAAI,OAAO,CAAC,GAAG,CAAC,oBAAoB,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC;IACpF,MAAM,kBAAkB,IAAI,OAAO,CAAC,GAAG,CAAC,sBAAsB,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC;IAEvF,MAAM,aAAa;QAAC;QAAU;QAAW;QAAoB;QAAiB;QAAS;KAAmB;IAC1G,MAAM,oBAAoB;QAAC;QAAS;QAAY;QAAc;QAAgB;KAAa;IAC3F,MAAM,sBAAsB;QAAC;KAAmB;IAEhD,2EAA2E;IAC3E,MAAM,0BAA0B,uIAAA,CAAA,4BAAyB;IAEzD,iGAAiG;IACjG,2DAA2D;IAE3D,6FAA6F;IAC7F,MAAM,6BAA6B,CAAC;QAClC,MAAM,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC;QACrC,MAAM,cAAc,QAAQ,OAAO,CAAC,QAAQ;QAE5C,6EAA6E;QAC7E,IAAI,CAAC,UAAU;YACb,OAAO;QACT;QAEA,kDAAkD;QAClD,IAAI,YAAY,UAAU,CAAC,oBAAoB;YAC7C,uFAAuF;YACvF,IAAI;gBACF,MAAM,cAAc,IAAI,IAAI;gBAC5B,MAAM,eAAe,YAAY,QAAQ;gBAEzC,iFAAiF;gBACjF,MAAM,eAAe,IAAI,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,iBAAiB;gBAEhF,qFAAqF;gBACrF,MAAM,mBAAmB,wBAAwB,IAAI,CAAC,CAAA,QAAS,aAAa,UAAU,CAAC;gBAEvF,qFAAqF;gBACrF,MAAM,8BAA8B,uIAAA,CAAA,iCAA8B;gBAClE,MAAM,kBAAkB,4BAA4B,IAAI,CAAC,CAAA,SAAU,aAAa,UAAU,CAAC;gBAE3F,QAAQ,GAAG,CAAC,uCAAuC;oBACjD,cAAc;oBACd,kBAAkB;oBAClB,iBAAiB;oBACjB,cAAc;gBAChB;gBAEA,OAAO,oBAAoB,mBAAmB;YAChD,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,kDAAkD;gBAC/D,OAAO;YACT;QACF;QAEA,yFAAyF;QACzF,IAAI;YACF,MAAM,cAAc,IAAI,IAAI;YAC5B,IAAI,YAAY,QAAQ,CAAC,UAAU,CAAC,oBAAoB;gBACtD,QAAQ,GAAG,CAAC,6DAA6D;oBACvE,UAAU;oBACV,cAAc,YAAY,QAAQ;oBAClC,aAAa;gBACf;gBACA,OAAO,MAAM,4CAA4C;YAC3D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,2CAA2C;QACxD,uDAAuD;QACzD;QAEA,0DAA0D;QAC1D,OAAO,2BAA2B,UAAU;IAC9C;IAEA,4EAA4E;IAC5E,MAAM,6BAA6B,CAAC,UAAU;QAC5C,IAAI;YACF,MAAM,YAAY,IAAI,IAAI;YAE1B,oFAAoF;YAEpF,wBAAwB;YACxB,MAAM,eAAe,UAAU,QAAQ;YAEvC,4EAA4E;YAC5E,2EAA2E;YAC3E,IAAI,uIAAA,CAAA,yBAAsB,CAAC,IAAI,CAAC,CAAA,cAAe,aAAa,UAAU,CAAC,eAAe;gBACpF,OAAO;YACT;YAEA,gFAAgF;YAChF,OAAO,uIAAA,CAAA,0BAAuB,CAAC,IAAI,CAAC,CAAA,cAAe,aAAa,UAAU,CAAC;QAC7E,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,8BAA8B;YAC3C,OAAO;QACT;IACF;IAEA,kDAAkD;IAClD,MAAM,2BAA2B,CAAC;QAChC,MAAM,WAAW,IAAI,OAAO,CAAC,GAAG,CAAC;QACjC,MAAM,cAAc,IAAI,OAAO,CAAC,QAAQ;QAExC,IAAI,CAAC,UAAU;YACb,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,IAAI;YACF,MAAM,cAAc,IAAI,IAAI;YAC5B,MAAM,eAAe,YAAY,QAAQ;YAEzC,QAAQ,GAAG,CAAC,kCAAkC;gBAC5C,aAAa;gBACb,UAAU;gBACV,cAAc;YAChB;YAEA,mDAAmD;YACnD,MAAM,0BAA0B,uIAAA,CAAA,4BAAyB,CAAC,IAAI,CAAC,CAAA,SAAU,aAAa,UAAU,CAAC;YAEjG,QAAQ,GAAG,CAAC,yCAAyC;gBACnD,cAAc;gBACd,yBAAyB;gBACzB,gBAAgB,uIAAA,CAAA,4BAAyB;YAC3C;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,wDAAwD;YACrE,OAAO;QACT;IACF;IAEA,gHAAgH;IAChH,2DAA2D;IAE3D,IAAI,mBAAmB,YAAY;QACjC,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,aAAa,IAAI,GAAG;QACnE,SAAS,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI;YAAE,QAAQ,CAAC;QAAE;QAC3D,OAAO;IACT;IAEA,uEAAuE;IACvE,wEAAwE;IACxE,IAAI,SAAS,UAAU,CAAC,oBAAoB;IAC1C,sFAAsF;IACtF,mGAAmG;IACrG,OAAO;QACL,sDAAsD;QACtD,IAAI,WAAW,IAAI,CAAC,CAAC,QAAU,SAAS,UAAU,CAAC,SAAS;YAC1D,IAAI,YAAY;gBACd,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,IAAI,GAAG;YAC5D;QACF;QAEA,IAAI,oBAAoB,QAAQ,CAAC,WAAW;YAC1C,IAAI,CAAC,cAAc,CAAC,UAAU;gBAC5B,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,IAAI,GAAG;YACxD;QACF;QAEA,IAAI,kBAAkB,IAAI,CAAC,CAAC,SAAW,SAAS,UAAU,CAAC,UAAU;YACnE,IAAI,CAAC,YAAY;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,IAAI,GAAG;YACxD;QACF;IACF;IAEA,4DAA4D;IAC5D,IAAI,SAAS,UAAU,CAAC,oBAAoB;QAC1C,qDAAqD;QACrD,MAAM,mBAAmB,2BAA2B;QAEpD,QAAQ,GAAG,CAAC,0CAA0C;YACpD,UAAU;YACV,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc;YACxC,kBAAkB;QACpB;QAEA,IAAI,CAAC,kBAAkB;YACrB,uEAAuE;YACvE,wDAAwD;YACxD,MAAM,cAAc,aAAa,sBAAsB;YAEvD,QAAQ,GAAG,CAAC,uEAAuE;gBACjF,eAAe;gBACf,aAAa;gBACb,YAAY;gBACZ,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc;YAC1C;YAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,aAAa,IAAI,GAAG;QAC3D;QAEA,yEAAyE;QACzE,QAAQ,GAAG,CAAC;QACZ,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,wEAAwE;IACxE,IAAI,uIAAA,CAAA,6BAA0B,CAAC,IAAI,CAAC,CAAC,QAAU,SAAS,UAAU,CAAC,SAAS;QAE1E,8DAA8D;QAC9D,MAAM,uBAAuB,yBAAyB;QACtD,QAAQ,GAAG,CAAC,yCAAyC;YACnD,UAAU;YACV,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc;YACxC,sBAAsB;QACxB;QAEA,IAAI,CAAC,sBAAsB;YACzB,MAAM,cAAc,CAAA,GAAA,uIAAA,CAAA,yBAAsB,AAAD,EAAE;YAE3C,QAAQ,GAAG,CAAC,4EAA4E;gBACtF,eAAe;gBACf,aAAa;gBACb,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc;YAC1C;YAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,aAAa,IAAI,GAAG;QAC3D;QAEA,qEAAqE;QACrE,QAAQ,GAAG,CAAC,iDAAiD;QAC7D,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,oFAAoF;IACpF,IAAI,wBAAwB,IAAI,CAAC,CAAC,QAAU,SAAS,UAAU,CAAC,SAAS;QAEvE,8DAA8D;QAC9D,MAAM,mBAAmB,2BAA2B;QACpD,QAAQ,GAAG,CAAC,+BAA+B;YACzC,UAAU;YACV,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc;YACxC,kBAAkB;QACpB;QAEA,IAAI,CAAC,kBAAkB;YACrB,MAAM,cAAc,CAAA,GAAA,uIAAA,CAAA,6BAA0B,AAAD,EAAE;YAE/C,QAAQ,GAAG,CAAC,yDAAyD;gBACnE,eAAe;gBACf,aAAa;gBACb,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc;YAC1C;YAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,aAAa,IAAI,GAAG;QAC3D;QAEA,uEAAuE;QACvE,MAAM,mBAAmB,IAAI,OAAO,CAAC,GAAG,CAAC;QAEzC,QAAQ,GAAG,CAAC,4CAA4C;YACtD;YACA,cAAc,CAAC,CAAC;YAChB,aAAa,kBAAkB;YAC/B,SAAS,mBAAmB,sBAAsB,iBAAiB,KAAK,IAAI;YAC5E,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc;QAC1C;QAEA,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,iBAAiB,KAAK,GAAG;YACvE,QAAQ,GAAG,CAAC,uCAAuC;YACnD,0EAA0E;YAC1E,MAAM,WAAW,WAAW,IAAI,OAAO,CAAC,MAAM;YAE9C,uFAAuF;YACvF,IAAI,CAAA,GAAA,uIAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW;gBAChC,MAAM,UAAU,mBAAmB;gBACnC,QAAQ,GAAG,CAAC,iDAAiD;gBAC7D,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,qBAAqB,EAAE,SAAS,EAAE,IAAI,GAAG;YACjF,OAAO;gBACL,QAAQ,IAAI,CAAC,sEAAsE;gBACnF,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,mBAAmB,IAAI,GAAG;YACjE;QACF;QAEA,QAAQ,GAAG,CAAC,qCAAqC;IACnD;IAGA,gCAAgC;IAEhC,IAAI,cAAc,YAAY;QAC5B,MAAM,WAAW,aACb,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,+DAA+D,IAAI,GAAG,KACpG,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,iDAAiD,IAAI,GAAG;QAC1F,SAAS,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI;YAAE,QAAQ,CAAC;QAAE;QACrD,OAAO;IACT;IAEA,IAAI,kBAAkB,YAAY;QAChC,MAAM,WAAW,aACb,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,wDAAwD,IAAI,GAAG,KAC7F,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,0CAA0C,IAAI,GAAG;QACnF,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;YAAE,QAAQ,CAAC;QAAE;QACzD,OAAO;IACT;IAEA,IAAI,aAAa,cAAc,YAAY;QACzC,IAAI,WAAW,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;QAEvC,MAAM,YAAY,IAAI,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC;QAC/C,IAAI,CAAC,WAAW;YACd,IAAI,SAAS;YACb,IAAI,QAAQ,UAAU,SAAS;iBAC1B,IAAI,QAAQ,UAAU,SAAS;iBAC/B,IAAI,QAAQ,kBAAkB,SAAS;YAE5C,MAAM,SAAS,aAAa,SAAS;YACrC,MAAM,cAAc,CAAC,gBAAgB,EAAE,OAAO,CAAC,EAAE,OAAO,kBAAkB,CAAC;YAE3E,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,aAAa,IAAI,GAAG;QAC3D;IACF;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AAEH"}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}